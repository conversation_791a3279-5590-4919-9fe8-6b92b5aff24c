🔹 Endpoint
POST /api/error-logger

📝 Request Body
لازم تبعت JSON بالـ Format ده:
{
  "deviceId": "string (unique device id)",
  "errMsg": "string (error message)",
  "teacherCode": "string (teacher unique code)",
  "studentCode": "string (student unique code)"
}

الحقول:
deviceId → رقم/ID للجهاز أو المستخدم (إجباري).
errMsg → نص الرسالة الخاصة بالخطأ (إجباري).
teacherCode → الكود الخاص بالمعلم (إجباري).
studentCode → الكود الخاص بالطالب (إجباري).

📤 Example Request
POST /api/error-logger
Content-Type: application/json
{
  "deviceId": "device-123456",
  "errMsg": "TypeError: Cannot read property 'x' of undefined",
  "teacherCode": "TCHR-001",
  "studentCode": "STUD-123"
}

📥 Response
✅ Success (201 Created)
{
  "statusCode": 201,
  "requestId": "df8b6a1d-3e17-4a83-93f3-823f5c2e7c92",
  "msg": "تم حفظ الـ error log بنجاح ✅"
}

❌ Failure (500 Internal Server Error)
{
  "statusCode": 500,
  "errMsg": "❌ حصلت مشكلة في حفظ اللوج:"
}

📊 Data Saved in Database
كل Log بيتخزن في Collection ErrorLog بالـ Format ده:
{
  "requestId": "uuid-unique-id",
  "deviceId": "device-123456",
  "teacherCode": "TCHR-001",
  "studentCode": "STUD-123",
  "errMsg": "TypeError: Cannot read property 'x' of undefined",
  "createdAt": "2025-08-20T12:00:00.000Z"
}