✅ Student Register API
Route: POST /api/student/register
Access: Public – متاح لأي طالب جديد للتسجيل
الوصف: تسجيل طالب جديد على المنصة وربطه بالمدرس من خلال teacherCode

📥 Request Body
{
  "deviceId": "string",          // ID الخاص بجهاز الطالب
  "teacherCode": "number",       // كود المدرس
  "fullname": "string",          // اسم الطالب بالكامل
  "studentPhone": "string",      // رقم موبايل الطالب (11 رقم)
  "guardianPhone": "string",     // رقم ولي الأمر (11 رقم)
  "grade": "string"              // المرحلة الدراسية (لازم تكون من المراحل المتاحة عند المدرس)
}
✅ كل الحقول مطلوبة، ولازم الـ studentPhone و guardianPhone يكونوا 11 رقم.

📤 Response (Success: 201)
{
  "statusCode": 201,
  "token": "JWT_TOKEN_HERE",
  "msg": "اهلا بيك معانا يا {studentName}, هذا كود الخاص بك {code}.",
  "fullname": "اسم الطالب",
  "grade": "المرحلة الدراسية",
  "studentPhone": "رقم الطالب",
  "guardianPhone": "رقم ولي الأمر",
  "cash": 0,

  "fullSections": [  // الأقسام المتاحة للطالب
    {
      "sectionId": "ObjectId",
      "title": "عنوان القسم",
      "description": "الوصف",
      "price": 150,
      "image": "url",
      "grade": "المرحلة الدراسية",
      "introductionVideo": "video_url"
    }
  ],

  "boughtSections": [],          // الأقسام المشتراة (فارغة عند التسجيل)
  "lectures": [],                // المحاضرات المشتراة (فارغة عند التسجيل)

  "notification": [              // الإشعارات الخاصة بالمدرس والمرحلة
    {
      "notificationOrder": 1,
      "title": "عنوان الإشعار",
      "description": "محتوى الإشعار",
      "image": "image_url"
    }
  ]
}

⚠️ Errors
الحالة	statusCode	errMsg
Missing Data	400	البيانات دي مش مكتملة!
Teacher Not Found	404	لا يوجد معلومات عن المدرس
Invalid Phone Numbers	400	رقم الهاتف مش مكتمل!
Invalid Grade	400	الصف الدراسي غير صالح، رجاءً اختر صفًا دراسيًا صحيحًا من القائمة المتاحة.
Student Already Exists	409	البينات دي متسجلة قبل كده يا نجم.
Server Error	500	فيه مشكلة فـ السيرفر، جرب تاني


✅ Student Login API
Route: POST /api/student/login
Access: Private – متاح فقط للطلاب المسجلين.
الوصف: تسجيل دخول الطالب باستخدام كود الطالب وكود المدرس وdeviceId.

📥 Request Body
{
  "deviceId": "string",         // ID الخاص بجهاز الطالب
  "teacherCode": "number",      // كود المدرس
  "studentCode": "string"       // كود الطالب
}
كل الحقول مطلوبة

📤 Response (Success: 200)
{
  "statusCode": 200,
  "msg": "✅ أهلًا بيك يا {studentName}، دخول ناجح.",
  "token": "JWT_TOKEN_HERE",
  "fullname": "اسم الطالب",
  "grade": "المرحلة الدراسية",
  "studentPhone": "رقم الطالب",
  "guardianPhone": "رقم ولي الأمر",
  "cash": 0,  // أو رصيد الطالب

  "fullSections": [  // الأقسام المتاحة للطالب في مرحلته
    {
      "sectionId": "ObjectId",
      "title": "عنوان القسم",
      "description": "الوصف",
      "price": 150,
      "image": "url",
      "grade": "المرحلة الدراسية",
      "introductionVideo": "video_url"
    }
  ],

  "boughtSections": ["sectionId1", "sectionId2", ...],  // الأقسام اللي الطالب اشتراها

  "lectures": [  // المحاضرات الخاصة بالأقسام المشتركة
    {
      "lectureOrder": 1,
      "title": "عنوان المحاضرة",
      "description": "الوصف",
      "videoId": "YouTubeVideoID",
      "thumbnail": "thumbnail_url",
      "duration": "10:23",
      "pdfUrl": "pdf_url",
      "examUrl": "exam_link"
    }
  ],

  "notifications": [  // الإشعارات الخاصة بالمرحلة والمدرس
    {
      "notificationOrder": 1,
      "title": "عنوان الإشعار",
      "description": "محتوى الإشعار",
      "image": "image_url"
    }
  ]
}

⚠️ Errors
الحالة	statusCode	errMsg
Missing Data	400	البيانات دي مش مكتملة!
Student Not Found	404	البينات دي مش متسجلة قبل كده يا نجم!
Teacher Not Found	404	لا يوجد معلومات عن المدرس
Unauthorized Teacher	403	مش مسموحلك تدخل القسم ده!
Wrong Device	403	الحساب ده مفتوح على جهاز تاني!
Blocked Account	403	الحساب ده متوقف دلوقتي, اتواصل مع الدعم!
Expired Subscription	401	انتهى اشتراكك أو لم تشترك بعد، برجاء التجديد أو الاشتراك.
Server Error	500	فيه مشكلة فـ السيرفر، جرب تاني