📌 Endpoint
GET /api/get-initial-data/:teacherCode

🔐 Access
nginx
Public (no authentication required)

🧠 Description
يرجع بيانات المدرس الأساسية باستخدام كود المدرس (teacherCode)،
تشمل رقم الموبايل، جريدات المنصة، روابط السوشيال، والنسخ المتاحة للتطبيق.

📥 URL Params
Param	Type	Required	Description
teacherCode	String	✅ Yes	كود المدرس (مثلاً: 58942758)

✅ Example Request
GET https://prof-academy-api.vercel.app/api/get-initial-data/58942758

📤 Response (200 OK)
{
  "statusCode": 200,
  "phone": "01017372176",
  "grads": [
    { "grade": "أولى" },
    { "grade": "تانية" }
  ],
  "socialLinks": [
    {
      "icon": "youtube",
      "url": "https://youtube.com/@physics-secrets"
    },
    {
      "icon": "facebook",
      "url": "https://facebook.com/physics-secrets"
    },
    {
      "icon": "tiktok",
      "url": "https://tiktok.com@physics-secrets"
    },
    {
      "icon": "instagram",
      "url": "https://instagram.com/physics-secrets"
    }
  ],
  "versions": [
    {
      "lastVersion": "1.0.3",
      "downloadLink": "https://example.com/download-app"
    }
  ]
}
❌ Error Responses
🔻 400 - Missing teacherCode
json
Copy
Edit
{
  "statusCode": 400,
  "errMsg": "البيانات دي مش مكتملة!"
}
🔻 404 - Teacher Not Found
json
Copy
Edit
{
  "statusCode": 404,
  "errMsg": "لا يوجد معلومات عن المدرس"
}
🔻 500 - Server Error
json
Copy
Edit
{
  "statusCode": 500,
  "errMsg": "فيه مشكلة فـ السيرفر، جرب تاني كده."
}