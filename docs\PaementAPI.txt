
الوصف:
الدالة دي مسئولة عن شحن محفظة الطالب باستخدام كود شحن (profCode) من المدرس. بعد التحقق من صحة البيانات والكود، يتم زيادة رصيد الطالب وحذف الكود من بيانات المدرس.

الراوت:
/api/student/recharge-wallet

المدخلات (Request Body):
المفتاح	النوع	الوصف
token	String	توكن JWT الخاص بالطالب (للتأكد من تسجيل الدخول)
teacherCode	String	الكود الخاص بالمدرس صاحب كود الشحن
profCode	String	كود الشحن نفسه المرسل من المدرس

الردود المحتملة (Responses):
✅ 200 OK (نجاح الشحن):
{
  "statusCode": 200,
  "msg": "✅ تم شحن المحفظة بمبلغ 50 بنجاح. رصيدك الحالي: 150"
}

⚠️ 400 Bad Request:
{
  "statusCode": 400,
  "errMsg": "يجب إدخال جميع الحقول المطلوبة."
}

⚠️ 401 Unauthorized:
{
  "statusCode": 401,
  "errMsg": "لازم تسجل دخول الأول علشان تخش للصفحة دي"
}

⚠️ 403 Forbidden:
{
  "statusCode": 403,
  "errMsg": "تأكد من إدخال كود صحيح!"
}

❌ 500 Internal Server Error:
{
  "statusCode": 500,
  "msg": "فيه مشكلة فـ السيرفر، جرب تاني"
}


الوصف:
الدالة دي مسئولة عن إتمام عملية شراء قسم (Section) من قبل الطالب.
بعد التحقق من بيانات الطالب والقسم، يتم خصم سعر القسم من رصيد الطالب وتسجيل العملية في جدول المدفوعات (Payment).

الراوت:
/api/student/buy-section

المدخلات (Request Body):
المفتاح	النوع	الوصف
token	String	توكن JWT الخاص بالطالب (للتأكد من تسجيل الدخول)
teacherCode	String	الكود الخاص بالمدرس صاحب القسم
sectionId	String	الـ ID الخاص بالقسم المراد شراؤه
paymentMethod	String	طريقة الدفع (مثلاً: profWallet, byTeacher, أو أي طريقة أخرى)

الردود المحتملة (Responses):

✅ 201 Created (نجاح الشراء):
{
  "statusCode": 201,
  "msg": "تم شراء القسم الرياضيات بنجاح 🎉"
}

⚠️ 400 Bad Request:
{
  "statusCode": 400,
  "errMsg": "يجب إدخال جميع الحقول المطلوبة."
}

⚠️ 401 Unauthorized:
{
  "statusCode": 401,
  "errMsg": "لازم تسجل دخول الأول علشان تخش للصفحة دي"
}

⚠️ 402 Payment Required:
{
  "statusCode": 402,
  "errMsg": "رصيدك ده مش كفاية, رصديدك 20 والقسم 50!"
}

⚠️ 403 Forbidden:
{
  "statusCode": 403,
  "errMsg": "القسم دي مش مخصصة لمرحلتك!"
}

⚠️ 404 Not Found (القسم أو المدرس مش موجود):

{
  "statusCode": 404,
  "errMsg": "القسم ده مش موجود!"
}

⚠️ 409 Conflict:

{
  "statusCode": 409,
  "errMsg": "القسم ده انت اشتريته قبل كده!"
}

❌ 500 Internal Server Error:
{
  "statusCode": 500,
  "msg": "فيه مشكلة فـ السيرفر، جرب تاني"
}